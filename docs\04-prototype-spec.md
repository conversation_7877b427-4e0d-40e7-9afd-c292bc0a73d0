# 青笺国风笔记 App · 高保真原型 & 视觉规范文档  
**版本**: v1.0  
**作者**: <PERSON>（UX 设计主管）  
**日期**: 2025-09-10  
**状态**: Ready for Dev  

---

## 1. 高保真页面流程（Figma 已同步）

| 编号 | 页面 | 关键状态 | 备注 |
| --- | --- | --- | --- |
| A | 启动闪屏 | 帘纹 alpha 0→1 600 ms | 无品牌 Logo，纯纹理 |
| B | 空笺首页 | 四象栏+空插画+新建 | 见 3.1 |
| C | 编辑页 | 全屏纸白+浮动工具条 | 见 3.2 |
| D | 手写画布 | 压感工具栏+晕散实时预览 | 见 3.3 |
| E | 设置/主题 | 国色切换+印章头像 | 见 3.4 |
| F | 导出长图 | 实时预览+钤印位置 | 见 3.5 |

---

## 2. 组件库（Design Token 已封装）

### 2.1 Color Token（Flutter 常量）
```yaml
qing60:  Color(0xFF8AB9C1)
paper0:  Color(0xFFFFFBF8)
rouge50: Color(0xFFC93756)
ink100:  Color(0xFF1E1E1E)
```

### 2.2 TextStyle Token
```yaml
h1: TextStyle(fontFamily: 'HarmonySong', fontSize: 28, fontWeight: FontWeight.w600, height: 1.2)
body: TextStyle(fontFamily: 'PingFang', fontSize: 16, fontWeight: FontWeight.w400, height: 1.6)
```

### 2.3 Radius & Elevation
```yaml
radiusButton: 24.0
radiusCard: 12.0
elevationCard: [0, 2, 8, 0] // x,y,blur,spread
```

---

## 3. 关键高保真界面

### 3.1 空笺首页（B 页）
- 背景：纸白 Paper-0 + 帘纹 3 层 SVG（不透明度 8%/6%/4%）  
- 四象栏：高 56 dp，左右 16 dp 安全边距，Icon 28×28 dp 线性  
- 空插画：320×320 dp 斜放青纹纸，冷金光束 15°，漏光渐变 radial（#FFEEDB→透明）  
- 文案：「且将心事付青笺」思源宋体 18 dp，字距 0.5 em  
- 新建按钮：圆角 24 dp，秘色填充，涟漪色 #6CA5AD（按下态）

### 3.2 编辑页（C 页）
- 顶部：四象栏透明→纸白滚动渐变 56 dp  
- 正文区：全屏纸白，左右 24 dp 页边距，首行缩进 2 em  
- 浮动工具条：高 48 dp，圆角 24 dp，底部 16 dp 居中，阴影 elevationCard  
- 光标：天青 2 dp 宽，blink 500 ms 周期  
- 选择菜单：圆角 8 dp，背景纸灰 Paper-10，选项高 40 dp

### 3.3 手写画布（D 页）
- 画布：#FFFBF8 2560×1440 px 矢量画布，支持 10 指+Apple Pencil  
- 压感算法：lineWidth = 0.4 + 0.009 × force (0–1) mm  
- 晕散：随机噪点 1 px，透明度 12%，半径 4 px  
- 工具栏：左浮动 48 dp 圆形按钮组，间距 12 dp，背景纸白 80% 不透明度  
- 橡皮：长按切换「墨迹干/湿」两种擦除模式

### 3.4 设置/主题（E 页）
- 国色主题列表：6 色圆形预览 56 dp，选中勾纹胭脂红  
- 印章头像：128×128 dp，SHA-256→篆体，边框 4 dp #C93756  
- 深色切换：Sun/Moon Icon 旋转 180° 300 ms  
- 声景音量：滑块左侧水滴 Icon，轨道天青 2 dp，Thumb 12 dp

### 3.5 导出长图（F 页）
- 预览区：1080×H 实时渲染，H 最小 1920 px  
- 钤印：右下角 120×120 dp，距边 64 dp  
- 留白：上下 10% 纸白无纹理，可拖动调整  
- 水印：0.3 透明度「青笺」篆体 16 dp，居中底部

---

## 4. 动效详细规格（已输出 Lottie JSON）

| 场景 | 文件 | 时长 | 帧率 | 大小 |
|----|----|-----|-----|-----|
| 帘纹展开 | lottie_qing_open.json | 800 ms | 60 fps | 68 KB |
| 纸面翻转 | lottie_paper_flip.json | 300 ms | 60 fps | 45 KB |
| 按钮涟漪 | lottie_ripple.json | 240 ms | 60 fps | 12 KB |

---

## 5. 插画源文件（Figma + SVG）

- 空笺插画：qing_empty.svg（320×320 dp，< 20 KB）  
- 断线鸿雁：qing_sync_fail.svg（240×160 dp，< 15 KB）  
- 冷金光束：qing_light_beam.svg（径向渐变，矢量）

---

## 6. 声景素材（已压缩 ogg）

- 水滴声：drop.ogg 0.3 s –16 LUFS  
- 窗棂风：wind_loop.ogg 1.2 s –20 LUFS 无缝  
- 远寺钟：bell_night.ogg 2.5 s –24 LUFS

---

## 7. 高保真走查 Checklist（供 Dev 自审）

- [ ] 帘纹 SVG 在 iPhone SE → iPad Pro 无拉伸 & GPU < 5%  
- [ ] 手写晕散 60 FPS，4 px 噪点不糊  
- [ ] 按钮涟漪中心误差 ≤ 4 dp  
- [ ] 导出长图钤印 300 DPI 放大无锯齿  
- [ ] 深色模式暖金漏痕色值 #E0C89A 与纸灰对比 ≥ 4.5:1  
- [ ] 空笺插画 VoiceOver 可读「空白笔记，点击落笔」  

---

## 8. 交付物清单（已上传 Figma & Git）

```
├─ figma/
│  ├─ QingJian_HiFi_v1.fig
│  └─ Component_Library_v1.fig
├─ assets/
│  ├─ svg/
│  ├─ lottie/
│  └─ ogg/
├─ token/
│  └─ qing_design_token.yaml
└─ docs/
   └─ 本文档_PDF
```

---

**高保真原型 & 视觉规范已完成，可直接进入开发阶段。**