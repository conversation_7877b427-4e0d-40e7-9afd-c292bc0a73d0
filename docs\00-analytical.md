「青笺」的品牌立意非常动人——把数字书写重新“慢化”，让每一次打开都像推开一扇花窗、研开一池松烟。

---

### 1. 品牌三层意象 → 产品关键词对照表

| 品牌意象 | 用户体感 | 产品落地关键词 |
| --- | --- | --- |
| 展笺 | 打开 App = 推窗 | 帘纹动效、冷金漏痕、单篇「纹种子」算法 |
| 研墨 | 降噪、沉浸 | 0.3s 水滴声、四象 UI（笔/墨/纸/光）、无推送 |
| 存世·寄世 | 离线优先、云随缘 | 本地 SQLite + 按需云同步、「折笺」「鸿雁」双按钮 |

---

### 2. 一级用户故事（供 PRD 用）

**角色：国风爱好者 & 慢书写人群**  
**主目标：在数字洪流里留一条“慢缝”**

1. 展笺  
   「作为用户，我希望每次新建笔记都看到独一无二的帘纹动效，好让我感知‘此刻只为我展开’。」
2. 研墨  
   「作为用户，我希望全程无推送、无广告，只有笔/墨/纸/光四象，好让我听得见松烟化开。」
3. 落笔  
   「作为用户，我希望手写/键盘/语音可无缝切换，且墨迹边缘有轻微‘晕散’，好让我保有‘墨落在纸’的手感。」
4. 存世  
   「作为用户，我希望默认离线保存，只有在我明确点击‘鸿雁’时才云同步，好让我的字句先被纸纹记住。」
5. 寄世  
   「作为用户，我希望可随时‘折笺入袖’导出加密 ZIP，也能生成一张‘青纹长图’分享，好让我的心事可传情。」

---

### 3. 核心功能清单（MVP 用）

- 帘纹引擎：每篇随机种子生成 3 层 SVG 帘纹，支持 6 款国色主题（天青／秘色／胭脂…）
- 四象 UI：顶部仅「笔」（新建）／「墨」（插入）／「纸」（切换帘纹）／「光」（夜间），底栏隐藏，上滑呼出
- 输入层：  
  - 键盘：富文本极简条（加粗＝浓墨，斜体＝飞白，下划线＝游丝）  
  - 手写：Apple Pencil 压感 → 矢量路径，回环算法模拟“晕散”  
  - 语音：本地讯飞离线包，转文字后自动断句加注标点
- 声景：水滴声 0.3s、窗棂风 1.2s、远寺钟 2.5s，三档音量／关闭
- 离线存储：SQLite + 本地文件库，默认不申请网络权限
- 云同步（可选）：WebDAV / iCloud Drive / 自托管 Git 仓库，端到端加密
- 导出：  
  - 折笺：加密 ZIP（含 .md + 资源）  
  - 鸿雁：青纹长图 1080×H、留白钤印（自动生成印章头像）
- 空笺态：无字时显示斜放青纹纸 + 冷金光束 + 提示「且将心事付青笺」

---

### 4. 非功能需求（NFR）初稿

| 维度 | 目标值 | 备注 |
| --- | --- | --- |
| 启动耗时 | ≤ 800 ms | 含帘纹初始化 |
| 手写延迟 | ≤ 20 ms | 120 Hz 屏 |
| 安装包体 | ≤ 60 MB | 含离线语音模型 |
| 离线容量 | 1 万汉字 ≈ 1 MB | 纯文本 |
| 能耗 | 10 分钟书写 ≤ 2% 电量 | 关语音输入 |
| 无障碍 | 字号 12–32 pt 可调 | 支持 VoiceOver |

---

### 5. 技术选型建议

- 跨端：Flutter + Rust 核心（帘纹、手写引擎）
- 数据库：Drift (SQLite) + ObjectBox 二进制大字段
- 云同步：libgit2 + age 加密
- 动效：Flutter CustomPainter + GLSL 冷金漏痕着色器
- 声景：ogg 循环包，音量随系统焦点自动淡出
- 主题：HCT 色域算法，保证国色在深浅模式均一致

---

### 6. 下一步可选深化路径

A. 继续细化 PRD → 我帮你把上述故事转成 YAML 模板，出完整 PRD 文档  
B. 直接出架构 → 先定义“帘纹引擎”接口、离线同步策略、数据模型  
C. 先做交互原型 → 输出 Figma 链接 + 可点击 Flutter Demo（最小故事 1.1）  
D. 品牌延展 → 命名印章头像算法、国色色值表、音效版权采购清单  

你只需回复：  
- 「A」——我立刻生成 PRD YAML 并走模板流程  
- 「B」——切架构师视角，出技术架构文档  
- 「C」——切 Scrum Master，把「展笺」故事拆成 1.1 卡片，进开发  
- 「D」——继续补品牌资产，输出视觉/声音/文案规范  

或任意补充/纠正方向。青笺已展，等你落墨。