# 青笺国风笔记 App · UI/UX 设计规范文档  
**版本**: v1.0  
**作者**: <PERSON>（UX 设计主管）  
**日期**: 2025-09-10  
**状态**: Draft  

---

## 1. 设计愿景
「展笺、研墨、落笔」——让每一次交互都完成一次"仪式转译"：  
- 数字动效 ≈ 窗棂漏光  
- 触控反馈 ≈ 松烟化开  
- 界面转场 ≈ 折笺入袖  

---

## 2. 品牌视觉锚点

| 意象 | 视觉关键词 | 禁用元素 |
|----|-----------|---------|
| 帘纹 | 半透明 SVG 斜织纹、冷金漏痕 | 几何扁平、Material 阴影 |
| 研墨 | 0.3 s 水纹涟漪、黑-灰渐变 | 高饱和撞色、弹跳动画 |
| 花窗 | 圆角+连续弧线、漏光光束 | 直角卡片、锐利分割 |

---

## 3. 国色体系（HCT 色域）

### 3.1 主色
- 天青 Qing-60 #8AB9C1（顶部导航/强调）  
- 秘色 Qing-70 #7EB4A7（主按钮）  

### 3.2 中性色
- 纸白 Paper-0 #FFFBF8  
- 纸灰 Paper-10 #F5F3F0  
- 墨黑 Ink-100 #1E1E1E  

### 3.3 语义色
- 胭脂 Rouge-50 #C93756（错误/印章）  
- 缃叶 Xiang-40 #D9C26A（警告）  

### 3.4 深色模式
所有颜色 -15% 明度，纸白→纸灰，保持 4.5:1 对比度。

---

## 4. 字体与排版

### 4.1 字体栈
- iOS：鸿蒙宋体（标题 24-28 pt）、苹方（正文 16 pt）  
- Android：思源宋体、Noto Sans CJK  
- 印章：自制篆体 TTF（≤32 字）

### 4.2 行距与段距
- 正文行距 1.6 em  
- 标题行距 1.2 em  
- 段前距 0.8 em、段后距 0

### 4.3 字重映射
- 导航栏：Semibold 600  
- 按钮：Medium 500  
- 正文：Regular 400  

---

## 5. 图标与插画

### 5.1 四象 Icon（28×28 dp）
- 笔：毛笔侧锋线性 icon  
- 墨：墨池波纹填充 icon  
- 纸：帘纹半透明矩形  
- 光：窗棂漏光放射状  

### 5.2 插画风格
- 线条：2 dp 连续曲线，端点圆角  
- 颜色：单色系+纸白负形  
- 动效：淡入 300 ms + 上移 8 dp（ease-out）

---

## 6. 布局与栅格

### 6.1 栅格
- 8 dp 基线网格  
- 横向 4 列（Phone）/8 列（Pad）  
- 安全边距 16 dp（Phone）/24 dp（Pad）

### 6.2 关键页面骨架
- 首页：顶部 56 dp 四象栏 + 中央「空笺」插画  
- 编辑页：全屏纸白画布 + 浮动极简工具条（高 48 dp）  
- 设置页：圆角卡片 12 dp + 帘纹背景 10% 不透明度

---

## 7. 组件规范

### 7.1 主按钮（CTA）
- 高 48 dp，圆角 24 dp  
- 默认：秘色填充 + 白色字  
- 按下：涟漪从触控点扩散 240 ms，末态 +12% 深度

### 7.2 文本输入
- 下划线样式，色值天青-60  
- 激活时线宽 2 dp，光标 20 ms 闪烁  
- 支持「松/适/紧」三档行距即时预览

### 7.3 手写画布
- 压感区间 0–400 g → 线宽 0.4–4 mm  
- 边缘「晕散」随机噪点 1 px，透明度 12%  
- 手掌排斥区 40 dp（左右）

### 7.4 帘纹生成器
- 3 层 SVG：经线 45°、纬线 –45°、冷金漏痕垂直  
- 随机种子 = note_id 哈希，保证唯一且可复现  
- 动效：alpha 自 0→60% 耗时 800 ms（Bezier）

---

## 8. 动效节奏表

| 场景 | 时长 | 曲线 | 备注 |
|----|-----|------|------|
| 新建笔记-帘纹展开 | 800 ms | Bezier(0.4,0,0.2,1) | 不可打断 |
| 按钮涟漪 | 240 ms | Bezier(0.2,0,0,1) | 可中断 |
| 转场（纸面翻转） | 300 ms | SharedAxis | 左右滑动手势 |
| 手写墨迹晕散 | 随写随算 | — | 16 ms 内完成 |

---

## 9. 声景与触觉

- 水滴声：0.3 s、–16 LUFS、单声道  
- 窗棂风：1.2 s 循环、–20 LUFS、无节拍  
- 远寺钟：2.5 s、–24 LUFS、夜间模式触发  
- 触觉：轻点 10 ms、书写 5 ms 微振（iPhone Taptic Engine）

---

## 10. 空状态与错误

### 10.1 空笺态
- 插画：斜放青纹纸 + 冷金光束  
- 文案：「且将心事付青笺」16 pt 思源宋体  
- 按钮：「落笔」圆角按钮居中

### 10.2 同步失败
- 插画：断线鸿雁  
- 文案：「雁未归，笺仍在」  
- 操作：「重寄」「折笺入袖」双按钮

---

## 11. 可访问性

- 字号 12–32 pt 系统跟随  
- 颜色对比 ≥ 4.5:1  
- VoiceOver 顺序：标题→正文→操作→帘纹（装饰可读可不读）  
- 手写支持左手模式（画布 180° 旋转）

---

## 12. 深色模式专项

- 纸白 → 纸灰 Paper-10  
- 墨黑 → 纯白 #FFFFFF  
- 帘纹不透明度 –10%  
- 冷金漏痕转为暖金 #E0C89A  
- 声景自动切换为「远寺钟」低音量

---

## 13. 切图与交付规范

- 命名：组件_状态_色值@2x.png  
- 示例：<EMAIL>  
- 格式：SVG 优先，PNG 仅用于插画  
- 尺寸：@1x/@2x/@3x 三件套，留白 1 px  
- Flutter 代码标注：色值、圆角、动效曲线、时长

---

## 14. 设计 Token（供开发直接引用）

```yaml
color:
  qing60: #8AB9C1
  paper0: #FFFBF8
  rouge50: #C93756
radius:
  card: 12 dp
  button: 24 dp
shadow:
  cardLight: [0,2,8,0 rgba(30,30,30,0.08)]
  cardDark:  [0,2,8,0 rgba(0,0,0,0.24)]
motion:
  easeOut: Bezier(0.4,0,0.2,1)
  easeIn:  Bezier(0.2,0,1,1)
```

---

## 15. 验证清单（供 UX 走查）

- [ ] 帘纹在不同屏幕尺寸无拉伸  
- [ ] 手写晕散无锯齿 60 FPS  
- [ ] 按钮涟漪中心点 = 触控点  
- [ ] 空状态插画 RTL 不翻转  
- [ ] 深色模式对比度 ≥ 4.5:1  
- [ ] 鸿雁长图导出钤印清晰度 ≥ 300 DPI  

---

**设计规范完毕，可直接交付开发团队进行组件化实现。**