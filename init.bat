@echo off
set "work_path=E:\PROJECT\AI_CODE\Work"
set "bmad_path=E:\PROJECT\AI_CODE\Prompt-bmad-core"
set "rules_path=E:\PROJECT\AI_CODE\BestPracticesRules"


mklink /J "%~dp0.bmad-core" "%bmad_path%\.bmad-core"
mklink /J "%~dp0.trae" "%bmad_path%\.trae"
mklink /J "%~dp0best-practices-rules" "%rules_path%"
mklink /J "%~dp0work" "%work_path%"

git init && git add  .\.gitignore && git commit -m "init commit"
